# -*- coding: utf-8 -*-
"""
图片AI分析模块
专门处理图片数据的AI分析功能
"""

import logging
from typing import Dict, Any, List, Optional
import asyncio

from services.llm_service import LLMService
from .PicPromt import PictureAnalysisPrompts
from .AvgConsumptionAi import avg_consumption_ai_analyzer
from .ConsumptionNumAi import consumption_num_ai_analyzer

logger = logging.getLogger(__name__)

class PictureAiAnalyzer:
    """图片AI分析器"""

    def __init__(self):
        """初始化图片AI分析器"""
        self.llm_service = LLMService()
        logger.info("图片AI分析器初始化完成")

    async def analyze_new_member_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年新增会员数据

        Args:
            monthly_data: 去年月度数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年新增会员数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = PictureAnalysisPrompts.get_new_member_last_year_analysis_prompt(monthly_data)

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下新增会员数据进行深入分析：

数据概况：
{self._format_monthly_data_summary(monthly_data)}

分析要求：
请按照以下格式生成具有洞见性和实操性的分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键洞察
2. 每个要点包含：数据洞察 + 具体可执行的实操建议
3. 重点关注：增长规模、季节性波动、留存质量、基础价值评估
4. 避免简单的数据描述，要提供深度分析和根因识别
5. 确保建议具体可执行，避免空泛的建议
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化

基础分析框架：
{analysis_prompt}

请生成按点格式的专业分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年新增会员数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"去年新增会员数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return PictureAnalysisPrompts.get_new_member_last_year_analysis_prompt(monthly_data)

    async def analyze_new_member_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年新增会员数据（包含对比分析）

        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年新增会员数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = PictureAnalysisPrompts.get_new_member_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_monthly_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下新增会员数据进行深入的对比分析，主要关注近期的变化给出改进建议：

今年数据概况：
{self._format_monthly_data_summary(this_year_data)}
{comparison_section}

分析要求：
请按照以下格式生成具有洞见性和实操性的对比分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键对比洞察
2. 每个要点包含：同比变化洞察 + 具体可执行的实操建议
3. 重点关注：增长对比、留存对比、运营效率对比、发展趋势
4. 深度分析变化背后的原因，提供根因识别
5. 确保建议具体可执行，包含明确的行动方向
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化

基础分析框架：
{analysis_prompt}

请生成按点格式的专业对比分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年新增会员数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"今年新增会员数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return PictureAnalysisPrompts.get_new_member_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

    def _format_monthly_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化月度数据摘要

        Args:
            monthly_data: 月度数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        try:
            # 计算汇总统计
            total_new = sum(item['new_members'] for item in monthly_data)
            total_unfollow = sum(item['new_unfollow_members'] for item in monthly_data)
            avg_unfollow_rate = sum(item['unfollow_rate'] for item in monthly_data) / len(monthly_data)

            # 找出最高和最低月份
            max_month = max(monthly_data, key=lambda x: x['new_members'])
            min_month = min(monthly_data, key=lambda x: x['new_members'])

            summary = f"""
- 总新增会员：{total_new:,}人
- 总取关会员：{total_unfollow:,}人
- 平均取关率：{avg_unfollow_rate:.1f}%
- 最高月份：{max_month['month']}（{max_month['new_members']:,}人）
- 最低月份：{min_month['month']}（{min_month['new_members']:,}人）
- 数据月份：{len(monthly_data)}个月
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化月度数据摘要失败: {str(e)}")
            return f"数据包含{len(monthly_data)}个月的记录"

    async def generate_all_new_member_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有新增会员相关的AI分析

        Args:
            this_year_data: 今年月度数据列表
            last_year_data: 去年月度数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成新增会员AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_new_member_last_year_data(last_year_data),
                self.analyze_new_member_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            last_year_analysis = results[0] if not isinstance(results[0], Exception) else "去年数据分析失败"
            this_year_analysis = results[1] if not isinstance(results[1], Exception) else "今年数据分析失败"

            analysis_results = {
                "new_member_add_last_year_analysis_report": last_year_analysis,
                "new_member_add_this_year_analysis_report": this_year_analysis
            }

            logger.info("新增会员AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成新增会员AI分析失败: {str(e)}")
            return {
                "new_member_add_last_year_analysis_report": "去年新增会员数据分析生成失败",
                "new_member_add_this_year_analysis_report": "今年新增会员数据分析生成失败"
            }

    async def analyze_member_consumption_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员消费数据

        Args:
            monthly_data: 去年月度消费数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年会员消费数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = PictureAnalysisPrompts.get_member_consumption_last_year_analysis_prompt(monthly_data)

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下会员消费数据进行深入分析：

数据概况：
{self._format_consumption_data_summary(monthly_data)}

分析要求：
请按照以下格式生成具有洞见性和实操性的分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键洞察
2. 每个要点包含：消费数据洞察 + 具体可执行的实操建议
3. 重点关注：消费规模、支付结构、季节性波动、消费质量评估
4. 避免简单的数据描述，要提供深度分析和根因识别
5. 确保建议具体可执行，避免空泛的建议
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化

基础分析框架：
{analysis_prompt}

请生成按点格式的专业分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年会员消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"去年会员消费数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return PictureAnalysisPrompts.get_member_consumption_last_year_analysis_prompt(monthly_data)

    async def analyze_member_consumption_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]] = None
    ) -> str:
        """
        分析今年会员消费数据（包含对比分析）

        Args:
            this_year_data: 今年月度消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年会员消费数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = PictureAnalysisPrompts.get_member_consumption_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_consumption_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下会员消费数据进行深入的对比分析，主要关注近期的变化给出改进建议：

今年数据概况：
{self._format_consumption_data_summary(this_year_data)}
{comparison_section}

分析要求：
请按照以下格式生成具有洞见性和实操性的对比分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键对比洞察
2. 每个要点包含：同比变化洞察 + 具体可执行的实操建议
3. 重点关注：消费增长对比、支付结构变化、运营效率对比、发展趋势
4. 深度分析变化背后的原因，提供根因识别
5. 确保建议具体可执行，包含明确的行动方向
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化

基础分析框架：
{analysis_prompt}

请生成按点格式的专业对比分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年会员消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"今年会员消费数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return PictureAnalysisPrompts.get_member_consumption_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

    async def generate_all_member_consumption_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员消费相关的AI分析

        Args:
            this_year_data: 今年月度消费数据列表
            last_year_data: 去年月度消费数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成会员消费AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_member_consumption_last_year_data(last_year_data),
                self.analyze_member_consumption_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            last_year_analysis = results[0] if not isinstance(results[0], Exception) else "去年消费数据分析失败"
            this_year_analysis = results[1] if not isinstance(results[1], Exception) else "今年消费数据分析失败"

            analysis_results = {
                "member_consumption_last_year_analysis_report": last_year_analysis,
                "member_consumption_this_year_analysis_report": this_year_analysis
            }

            logger.info("会员消费AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成会员消费AI分析失败: {str(e)}")
            return {
                "member_consumption_last_year_analysis_report": "去年会员消费数据分析生成失败",
                "member_consumption_this_year_analysis_report": "今年会员消费数据分析生成失败"
            }

    def _format_consumption_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化月度消费数据摘要

        Args:
            monthly_data: 月度消费数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        summary_lines = []
        total_amount_sum = 0
        prepay_amount_sum = 0
        cash_amount_sum = 0

        for item in monthly_data:
            month = item.get('month', '未知月份')
            total_amount = item.get('total_actual_amount', 0)
            prepay_amount = item.get('prepay_actual_amount', 0)
            cash_amount = item.get('actual_amount', 0)
            prepay_ratio = item.get('prepay_ratio', 0)
            cash_ratio = item.get('cash_ratio', 0)

            total_amount_sum += total_amount
            prepay_amount_sum += prepay_amount
            cash_amount_sum += cash_amount

            summary_lines.append(
                f"{month}: 总实收{total_amount:.1f}万元, 储值{prepay_amount:.1f}万元({prepay_ratio:.1f}%), "
                f"现金{cash_amount:.1f}万元({cash_ratio:.1f}%)"
            )

        # 添加汇总信息
        avg_total = total_amount_sum / len(monthly_data) if monthly_data else 0
        avg_prepay_ratio = (prepay_amount_sum / total_amount_sum * 100) if total_amount_sum > 0 else 0
        avg_cash_ratio = (cash_amount_sum / total_amount_sum * 100) if total_amount_sum > 0 else 0

        summary_lines.append(f"\n汇总: 月均总实收{avg_total:.1f}万元, 储值占比{avg_prepay_ratio:.1f}%, 现金占比{avg_cash_ratio:.1f}%")

        return "\n".join(summary_lines)

    async def analyze_avg_consumption_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员平均消费数据

        Args:
            monthly_data: 去年月度平均消费数据列表

        Returns:
            str: AI分析结果
        """
        return await avg_consumption_ai_analyzer.analyze_avg_consumption_last_year_data(monthly_data)

    async def analyze_avg_consumption_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员平均消费数据（包含对比分析）

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        return await avg_consumption_ai_analyzer.analyze_avg_consumption_this_year_data(this_year_data, last_year_data)

    async def generate_all_avg_consumption_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员平均消费相关的AI分析

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年月度平均消费数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        return await avg_consumption_ai_analyzer.generate_all_avg_consumption_analysis(this_year_data, last_year_data)

    async def analyze_consumption_num_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员消费数量数据

        Args:
            monthly_data: 去年月度消费数量数据列表

        Returns:
            str: AI分析结果
        """
        return await consumption_num_ai_analyzer.analyze_consumption_num_last_year_data(monthly_data)

    async def analyze_consumption_num_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员消费数量数据（包含对比分析）

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        return await consumption_num_ai_analyzer.analyze_consumption_num_this_year_data(this_year_data, last_year_data)

    async def generate_all_consumption_num_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员消费数量相关的AI分析

        Args:
            this_year_data: 今年月度消费数量数据列表
            last_year_data: 去年月度消费数量数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        return await consumption_num_ai_analyzer.generate_all_consumption_num_analysis(this_year_data, last_year_data)