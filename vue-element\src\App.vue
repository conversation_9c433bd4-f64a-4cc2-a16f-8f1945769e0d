<template>
  <div id="app">
    <header class="app-header">
      <div class="header-content">
        <h1 class="app-title">
          <el-icon><TrendCharts /></el-icon>
          PPT报告生成系统
        </h1>
        <div class="header-info">
          <span class="current-time">{{ currentTime }}</span>
        </div>
      </div>
    </header>

    <!-- 导航菜单 -->
    <nav class="app-nav">
      <div class="nav-content">
        <el-menu
          :default-active="activeMenu"
          mode="horizontal"
          @select="handleMenuSelect"
          class="nav-menu"
        >
          <el-menu-item index="/ppt-report">
            <el-icon><Document /></el-icon>
            <span>PPT报告生成</span>
          </el-menu-item>
        </el-menu>
      </div>
    </nav>
    
    <main class="app-main">
      <router-view />
    </main>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { TrendCharts, Document } from '@element-plus/icons-vue'

export default {
  name: 'App',
  setup() {
    const currentTime = ref('')
    const router = useRouter()
    const route = useRoute()
    let timer = null

    // 当前激活的菜单项
    const activeMenu = computed(() => route.path)

    // 更新时间
    const updateTime = () => {
      const now = new Date()
      currentTime.value = now.toLocaleString('zh-CN')
    }

    // 处理菜单选择
    const handleMenuSelect = (index) => {
      if (index !== route.path) {
        router.push(index)
      }
    }

    onMounted(() => {
      updateTime()
      timer = setInterval(updateTime, 1000)
    })

    onUnmounted(() => {
      if (timer) {
        clearInterval(timer)
      }
    })

    return {
      currentTime,
      activeMenu,
      handleMenuSelect,
      TrendCharts,
      Document
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Arial, sans-serif;
  background: #f5f7fa;
  color: #333;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.app-title {
  font-size: 24px;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-time {
  font-size: 14px;
  opacity: 0.9;
}

/* 导航菜单样式 */
.app-nav {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.nav-menu {
  border-bottom: none;
}

.nav-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  color: #303133 !important;
  border-bottom: 2px solid transparent;
  font-weight: 500;
}

.nav-menu .el-menu-item:hover {
  background-color: #f5f7fa;
  color: #303133 !important;
}

.nav-menu .el-menu-item.is-active {
  color: #303133 !important;
  border-bottom-color: #409eff;
  background-color: #ecf5ff;
}

.nav-menu .el-menu-item span {
  color: #303133 !important;
}

.nav-menu .el-menu-item:hover span {
  color: #303133 !important;
}

.nav-menu .el-menu-item.is-active span {
  color: #303133 !important;
}

.nav-menu .el-menu-item .el-icon {
  margin-right: 8px;
}

.app-main {
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 20px;
  overflow: visible;
  min-height: calc(100vh - 120px); /* 调整高度，考虑导航菜单 */
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 15px 0;
  }

  .app-title {
    font-size: 20px;
    margin-bottom: 10px;
  }

  .nav-content {
    padding: 0 10px;
  }

  .nav-menu .el-menu-item {
    font-size: 14px;
    height: 45px;
    line-height: 45px;
  }

  .app-main {
    padding: 10px;
    min-height: calc(100vh - 140px); /* 移动端调整 */
  }
}
</style>
