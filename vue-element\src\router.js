import { createRouter, createWebHistory } from 'vue-router'
import PPTReportGenerator from './views/PPTreport/PPTReportGenerator.vue'
import NotFound from './views/404.vue'

const routes = [
  {
    path: '/',
    redirect: '/ppt-report'
  },
  {
    path: '/ppt-report',
    name: 'PPTReportGenerator',
    component: PPTReportGenerator,
    meta: {
      title: 'PPT报告生成'
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
