# -*- coding: utf-8 -*-
"""
会员平均消费数据AI分析器
提供会员平均消费数据的智能分析功能
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional

from services.llm_service import LLMService
from .AvgConsumptionPromt import AvgConsumptionAnalysisPrompts

logger = logging.getLogger(__name__)

class AvgConsumptionAiAnalyzer:
    """会员平均消费数据AI分析器"""

    def __init__(self):
        """初始化会员平均消费数据AI分析器"""
        self.llm_service = LLMService()
        logger.info("会员平均消费数据AI分析器初始化完成")

    async def analyze_avg_consumption_last_year_data(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        分析去年会员平均消费数据

        Args:
            monthly_data: 去年月度平均消费数据列表

        Returns:
            str: AI分析结果
        """
        try:
            if not monthly_data:
                return "去年会员平均消费数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = AvgConsumptionAnalysisPrompts.get_avg_consumption_last_year_analysis_prompt(monthly_data)

            # 构建完整的AI分析请求
            full_prompt = f"""
作为专业的数据分析师，请基于以下会员平均消费数据进行深入分析：

数据概况：
{self._format_avg_consumption_data_summary(monthly_data)}

分析要求：
请按照以下格式生成具有洞见性和实操性的分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键洞察
2. 每个要点包含：数据洞察 + 具体可执行的实操建议
3. 重点关注：首次消费转化、复购行为、单均消费水平、人均贡献效率
4. 避免简单的数据描述，要提供深度分析和根因识别
5. 确保建议具体可执行，避免空泛的建议
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化

基础分析框架：
{analysis_prompt}

请生成按点格式的专业分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("去年会员平均消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"去年会员平均消费数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return AvgConsumptionAnalysisPrompts.get_avg_consumption_last_year_analysis_prompt(monthly_data)

    async def analyze_avg_consumption_this_year_data(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """
        分析今年会员平均消费数据（包含对比分析）

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年同期数据列表（用于对比）

        Returns:
            str: AI分析结果
        """
        try:
            if not this_year_data:
                return "今年会员平均消费数据分析：数据不足，无法进行有效分析。"

            # 生成分析提示词
            analysis_prompt = AvgConsumptionAnalysisPrompts.get_avg_consumption_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

            # 构建完整的AI分析请求
            comparison_section = ""
            if last_year_data:
                comparison_section = f"""
去年同期数据对比：
{self._format_avg_consumption_data_summary(last_year_data[:len(this_year_data)])}
"""

            full_prompt = f"""
作为专业的数据分析师，请基于以下会员平均消费数据进行深入的对比分析，主要关注近期的变化给出改进建议：

今年数据概况：
{self._format_avg_consumption_data_summary(this_year_data)}
{comparison_section}

分析要求：
请按照以下格式生成具有洞见性和实操性的对比分析报告：
1. 使用"1-4"作为项目符号，按点列出3-4个关键对比洞察
2. 每个要点包含：对比数据洞察 + 具体可执行的实操建议
3. 重点关注：首次消费变化趋势、复购行为改善、单均消费提升、人均贡献优化
4. 避免简单的数据描述，要提供深度对比分析和策略建议
5. 确保建议具体可执行，避免空泛的建议
6. 每个建议之间不要添加空行，也禁止添加加粗等格式
7. 如果分析不同月份直接的差异，要指出几月对比几月，明确对比的对象，更多的将注意力关注到近期的变化

基础分析框架：
{analysis_prompt}

请生成按点格式的专业对比分析报告（每点35-45字）：
"""

            # 调用LLM服务进行分析
            analysis_result = await self.llm_service.generate_response(full_prompt)

            if analysis_result and analysis_result.strip():
                logger.info("今年会员平均消费数据AI分析完成")
                return analysis_result.strip()
            else:
                logger.warning("AI分析返回空结果，使用默认分析")
                return analysis_prompt

        except Exception as e:
            logger.error(f"今年会员平均消费数据AI分析失败: {str(e)}")
            # 返回基础分析作为备用
            return AvgConsumptionAnalysisPrompts.get_avg_consumption_this_year_analysis_prompt(
                this_year_data, last_year_data or []
            )

    async def generate_all_avg_consumption_analysis(
        self,
        this_year_data: List[Dict[str, Any]],
        last_year_data: List[Dict[str, Any]]
    ) -> Dict[str, str]:
        """
        生成所有会员平均消费相关的AI分析

        Args:
            this_year_data: 今年月度平均消费数据列表
            last_year_data: 去年月度平均消费数据列表

        Returns:
            Dict: 包含所有分析结果的字典
        """
        try:
            logger.info("开始生成会员平均消费AI分析...")

            # 并行执行两个分析任务
            tasks = [
                self.analyze_avg_consumption_last_year_data(last_year_data),
                self.analyze_avg_consumption_this_year_data(this_year_data, last_year_data)
            ]

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理结果
            last_year_analysis = results[0] if not isinstance(results[0], Exception) else "去年平均消费数据分析失败"
            this_year_analysis = results[1] if not isinstance(results[1], Exception) else "今年平均消费数据分析失败"

            analysis_results = {
                "avg_consumption_last_year_analysis_report": last_year_analysis,
                "avg_consumption_this_year_analysis_report": this_year_analysis
            }

            logger.info("会员平均消费AI分析生成完成")
            return analysis_results

        except Exception as e:
            logger.error(f"生成会员平均消费AI分析失败: {str(e)}")
            return {
                "avg_consumption_last_year_analysis_report": "去年会员平均消费数据分析失败，请检查数据源和分析逻辑。",
                "avg_consumption_this_year_analysis_report": "今年会员平均消费数据分析失败，请检查数据源和分析逻辑。"
            }

    def _format_avg_consumption_data_summary(self, monthly_data: List[Dict[str, Any]]) -> str:
        """
        格式化会员平均消费数据摘要

        Args:
            monthly_data: 月度平均消费数据列表

        Returns:
            str: 格式化的数据摘要
        """
        if not monthly_data:
            return "无数据"

        try:
            # 计算关键指标
            first_consume_amounts = [item.get('first_consume_amount', 0) for item in monthly_data]
            repeat_consume_amounts = [item.get('repeat_consume_amount', 0) for item in monthly_data]
            avg_consume_amounts = [item.get('avg_consume_amount', 0) for item in monthly_data]
            avg_contributions = [item.get('avg_contribution', 0) for item in monthly_data]

            avg_first_consume = sum(first_consume_amounts) / len(first_consume_amounts)
            avg_repeat_consume = sum(repeat_consume_amounts) / len(repeat_consume_amounts)
            avg_consume_amount = sum(avg_consume_amounts) / len(avg_consume_amounts)
            avg_contribution = sum(avg_contributions) / len(avg_contributions)

            total_first_consume = sum(first_consume_amounts)
            total_repeat_consume = sum(repeat_consume_amounts)

            # 计算复购占比
            total_consume = total_first_consume + total_repeat_consume
            repeat_ratio = (total_repeat_consume / total_consume * 100) if total_consume > 0 else 0

            summary = f"""
数据期间：{len(monthly_data)}个月
首次消费：月均{avg_first_consume:.1f}元，累计{total_first_consume:.1f}元
再次消费：月均{avg_repeat_consume:.1f}元，累计{total_repeat_consume:.1f}元，占比{repeat_ratio:.1f}%
会员单均消费：月均{avg_consume_amount:.1f}元
会员人均贡献：月均{avg_contribution:.1f}元
月度数据：{[f"{item.get('month', 'N/A')}月({item.get('first_consume_amount', 0):.1f}/{item.get('repeat_consume_amount', 0):.1f}/{item.get('avg_consume_amount', 0):.1f}/{item.get('avg_contribution', 0):.1f})" for item in monthly_data]}
"""
            return summary.strip()

        except Exception as e:
            logger.error(f"格式化会员平均消费数据摘要失败: {str(e)}")
            return f"数据格式化失败: {str(e)}"


# 创建全局分析器实例
avg_consumption_ai_analyzer = AvgConsumptionAiAnalyzer()