# -*- coding: utf-8 -*-
"""
测试修复后的会员消费数量图片生成
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def test_consumption_num_fixed():
    """测试修复后的会员消费数量图片生成"""
    
    try:
        print("=" * 60)
        print("测试修复后的会员消费数量图片生成")
        print("=" * 60)

        # 1. 导入模块
        print("1. 导入模块...")
        from api.PPTreport.picture.ConsumptionNumPic import create_consumption_num_pic_generator
        from api.PPTreport.picture.PictureSave import PPTImageManager
        print("✓ 模块导入成功")

        # 2. 创建测试环境
        print("\n2. 创建测试环境...")
        test_bid = "1768303269"  # 使用用户提到的bid
        image_manager = PPTImageManager(test_bid)
        generator = create_consumption_num_pic_generator(test_bid, image_manager)
        print(f"✓ 测试环境创建成功")
        print(f"  会话目录: {image_manager.session_dir}")

        # 3. 测试参数
        print("\n3. 准备测试参数...")
        test_params = {
            'bid': test_bid,
            'sid': None,
            'end_date': '2025-07-30'  # 使用当前日期
        }
        print(f"✓ 测试参数: {test_params}")

        # 4. 测试完整流程
        print("\n4. 测试完整流程...")
        try:
            result = await generator.generate_consumption_num_charts(test_params)
            print(f"✓ 完整流程测试成功，生成 {len(result)} 个结果")

            for key, value in result.items():
                print(f"  {key}: {value}")

                # 检查图片文件是否存在
                if key.startswith('consumption_num_') and key.endswith(('_last_year', '_this_year')):
                    if isinstance(value, str) and Path(value).exists():
                        file_size = Path(value).stat().st_size
                        print(f"    ✓ 文件存在，大小: {file_size} 字节")
                    elif isinstance(value, str):
                        print(f"    ✗ 文件不存在: {value}")
                    else:
                        print(f"    ℹ 非文件路径结果: {type(value)}")

        except Exception as full_error:
            print(f"✗ 完整流程测试失败: {full_error}")
            import traceback
            traceback.print_exc()

            # 尝试单独测试数据获取
            print("\n4.1 尝试单独测试数据获取...")
            try:
                from datetime import datetime, timedelta
                end_date = datetime.strptime(test_params['end_date'], '%Y-%m-%d')
                this_year = end_date.year
                last_year = this_year - 1

                # 测试去年数据
                last_year_ranges = generator._generate_monthly_ranges(last_year, 1, 12)
                print(f"去年时间范围: {len(last_year_ranges)} 个月")

                last_year_data = await generator._fetch_monthly_data(last_year_ranges, test_params)
                print(f"去年数据获取成功: {len(last_year_data)} 条记录")

                if last_year_data:
                    print(f"示例数据: {last_year_data[0]}")

            except Exception as data_error:
                print(f"✗ 数据获取测试失败: {data_error}")
                import traceback
                traceback.print_exc()

        # 5. 测试PicAcquisition集成
        print("\n5. 测试PicAcquisition集成...")
        try:
            from api.PPTreport.PicAcquisition import PicAcquisitionService
            pic_service = PicAcquisitionService()
            pic_result = await pic_service._generate_consumption_num_pictures(test_params, image_manager)
            print(f"✓ PicAcquisition集成测试成功，生成 {len(pic_result)} 个结果")
            
            for key, value in pic_result.items():
                print(f"  {key}: {value}")
                
        except Exception as pic_error:
            print(f"✗ PicAcquisition集成测试失败: {pic_error}")

        # 6. 列出生成的文件
        print("\n6. 列出生成的文件...")
        try:
            session_dir = Path(image_manager.session_dir)
            if session_dir.exists():
                png_files = list(session_dir.glob("*.png"))
                print(f"✓ 找到 {len(png_files)} 个PNG文件:")
                for file_path in png_files:
                    file_size = file_path.stat().st_size
                    print(f"  - {file_path.name}: {file_size} 字节")
            else:
                print("✗ 会话目录不存在")
        except Exception as list_error:
            print(f"✗ 列出文件失败: {list_error}")

        print("\n" + "=" * 60)
        print("✓ 修复后的测试完成！")
        print("=" * 60)

        return True

    except Exception as e:
        print(f"\n✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(test_consumption_num_fixed())
    
    if success:
        print("\n🎉 修复后的测试完成！")
        sys.exit(0)
    else:
        print("\n❌ 修复后的测试失败")
        sys.exit(1)
